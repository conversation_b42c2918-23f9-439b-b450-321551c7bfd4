import React from "react";
import { Link } from "react-router-dom";
import {
  TrendingUp,
  Users,
  Database,
  Target,
  Zap,
  ExternalLink,
  Check,
} from "lucide-react";

const SupplyChainOptimizationDetail: React.FC = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              经营端 - 供应链协同优化智能决策解决方案
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              通过数据融合与实时决策，实现多目标全局优化，提升供应链协同效率
            </p>
          </div>
        </div>
      </section>

      {/* 痛点挑战 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">痛点挑战</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              供应链协同优化面临的核心挑战与痛点
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 痛点1：多资源协同困难 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-10 w-10 text-red-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                多资源协同困难
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                设备、人力、物料、运输等资源需同步优化，人工调度难以兼顾多重约束（如设备产能、工人技能、物料到货时间），容易产能不必要的浪费。
              </p>
            </div>

            {/* 痛点2：动态响应能力差 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-orange-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-10 w-10 text-orange-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                动态响应能力差
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                订单变更、设备故障、紧急插单等突发情况需快速重调度，人工调整滞后且易出错，容易导致交付延迟、库存积压、客户满意度下降。
              </p>
            </div>

            {/* 痛点3：瓶颈资源利用率低 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-yellow-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-10 w-10 text-yellow-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                瓶颈资源利用率低
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                关键设备或高技能人力成为瓶颈，传统排产忽视其负载平衡，导致整体产能受限，整体效率低下。
              </p>
            </div>

            {/* 痛点4：不确定性应对不足 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Database className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                不确定性应对不足
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                需求波动、供应商延迟、良品率变化等不确定性因素难量化，人工调度依赖经验且保守，过度备料增加成本、缺料容易导致停产。
              </p>
            </div>

            {/* 痛点5：成本与效率难以平衡 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-purple-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-10 w-10 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                成本与效率难以平衡
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                人工调度难以同时优化成本（能耗、库存、运输）、效率（交付周期、设备OEE）等多目标，局部优化牺牲全局利益。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 目标价值 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">目标价值</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              通过智能化技术实现供应链协同优化的核心价值
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* 多资源协同优化 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-green-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                多资源协同优化
              </h3>
              <p className="text-gray-600 text-center mb-6">
                全局资源智能调度
              </p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    精确建模设备、人力、物料等约束条件，生成可行解
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    通过交叉、变异操作搜索全局最优调度方案
                  </span>
                </div>
              </div>
            </div>

            {/* 动态实时调度 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                动态实时调度
              </h3>
              <p className="text-gray-600 text-center mb-6">智能响应变化</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    训练智能体根据实时状态（订单变化、设备异常）自主决策
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    滚动优化未来2小时排产
                  </span>
                </div>
              </div>
            </div>

            {/* 瓶颈资源智能分配 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-purple-100">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                瓶颈资源智能分配
              </h3>
              <p className="text-gray-600 text-center mb-6">优化关键资源</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">识别瓶颈资源，优先分配任务并设置缓冲</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    通过负载均衡算法动态分配任务至并行设备
                  </span>
                </div>
              </div>
            </div>

            {/* 鲁棒性调度应对不确定性 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-orange-100">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Database className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                鲁棒性调度应对不确定性
              </h3>
              <p className="text-gray-600 text-center mb-6">抗干扰能力强</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-orange-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">将需求波动、供应延迟建模为概率分布</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-orange-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    生成抗干扰方案，构建最坏场景下的可行解
                  </span>
                </div>
              </div>
            </div>

            {/* 多目标平衡优化 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-teal-100">
              <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="h-8 w-8 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                多目标平衡优化
              </h3>
              <p className="text-gray-600 text-center mb-6">全面平衡各项指标</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-teal-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">同时优化成本、交期、能耗等目标，输出帕累托最优解集</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-teal-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    设置加权评分模型，并设定优先级权重
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 应用案例 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">应用案例</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示供应链协同优化在复杂场景中的卓越表现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {/* 案例1：纸制品加工仓储配载协同 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="纸制品加工仓储配载协同"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    仓储协同优化
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某纸制品加工仓储配载协同项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  仓储协同优化
                </p>
                <div className="mb-6">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    通过数据仿真，进行生产下线、选择存储缓存区、规划RGV小车路径、决定板链运输方向、进行配载发运的全过程，设置基于整个工厂的模拟结果选择的最优决策，以实现配载发运区和缓存区WIP协同无阻塞。
                  </p>
                </div>

                <Link
                  to="/cases?case=paper-processing-supply-chain"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例2：通信集团智家产品销量预测 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="通信集团智家产品销量预测"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    需求预测
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某通信集团智家产品销量预测项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  需求预测
                </p>
                <div className="mb-6">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    为解决以光猫、机顶盒为典型代表的存货库存高企难题，某通信集团供应链管理部使用AI需求预测、替代人为经验需求预测。通过引入算法对市场营销物资光猫和机顶盒的需求进行预测，实现了自动化生成申购单，从源头对需求进行有效管控，基本做到了采购和需求的供需平衡。
                  </p>
                </div>

                <Link
                  to="/cases?case=telecom-demand-forecasting"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SupplyChainOptimizationDetail;

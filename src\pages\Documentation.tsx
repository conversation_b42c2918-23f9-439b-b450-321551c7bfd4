import React, { useState } from 'react';
import { FileText, Code, BookOpen, Download, ExternalLink, Calendar, FileIcon, Grid3X3 } from 'lucide-react';

const Documentation: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    {
      id: 'all',
      name: '全部',
      icon: <Grid3X3 className="h-5 w-5" />,
      color: 'from-gray-600 to-gray-700',
      bgColor: 'from-gray-50 to-gray-100'
    },
    {
      id: 'manual',
      name: '产品用户手册',
      icon: <FileText className="h-5 w-5" />,
      color: 'from-theme-600 to-theme-700',
      bgColor: 'from-theme-50 to-theme-100'
    },
    {
      id: 'technical',
      name: '技术文档',
      icon: <Code className="h-5 w-5" />,
      color: 'from-secondary-600 to-secondary-700',
      bgColor: 'from-secondary-50 to-secondary-100'
    },
    {
      id: 'examples',
      name: '样例代码',
      icon: <BookOpen className="h-5 w-5" />,
      color: 'from-theme-800 to-secondary-600',
      bgColor: 'from-warm-50 to-warm-100'
    },
    {
      id: 'downloads',
      name: '资源下载',
      icon: <Download className="h-5 w-5" />,
      color: 'from-secondary-700 to-theme-600',
      bgColor: 'from-secondary-50 to-theme-50'
    }
  ];

  const manualDocs = [
    {
      title: 'OptSuite产品用户手册',
      description: '详细介绍OptSuite的功能特性、安装配置和使用方法',
      fileType: 'PDF',
      fileSize: '15.2 MB',
      lastUpdated: '2024-01-15',
      downloadUrl: '/downloads/OptSuite-User-Manual.pdf',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    }
  ];

  const technicalDocs = [
    {
      title: 'OptSuite技术文档',
      description: '包含API接口文档、架构设计和技术规范',
      fileType: 'PDF',
      fileSize: '8.7 MB',
      lastUpdated: '2024-01-18',
      downloadUrl: '/downloads/OptSuite-Technical-Documentation.pdf',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    }
  ];

  const codeExamples = [
    {
      title: 'OptSuite样例代码',
      description: '包含Python、Java、C++等多种语言的示例代码和最佳实践',
      fileType: 'PDF',
      fileSize: '5.3 MB',
      lastUpdated: '2024-01-12',
      downloadUrl: '/downloads/OptSuite-Code-Examples.pdf',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    }
  ];

  const downloads = [
    {
      title: 'OptSuite软件下载',
      description: '最新版本的OptSuite软件安装包和相关工具',
      isExternal: true,
      externalUrl: 'https://download.optsuite.com',
      version: 'v2.1.0',
      lastUpdated: '2024-01-20',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    }
  ];

  const handleDownload = (url: string, filename: string) => {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExternalLink = (url: string) => {
    window.open(url, '_blank');
  };

  const getCurrentDocs = () => {
    switch (activeCategory) {
      case 'all':
        return [...manualDocs, ...technicalDocs, ...codeExamples, ...downloads];
      case 'manual':
        return manualDocs;
      case 'technical':
        return technicalDocs;
      case 'examples':
        return codeExamples;
      case 'downloads':
        return downloads;
      default:
        return [...manualDocs, ...technicalDocs, ...codeExamples, ...downloads];
    }
  };

  const renderDocumentCard = (doc: any, index: number) => {
    const isExternal = doc.isExternal;

    return (
      <div key={index} className="group">
        <div className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-all duration-300 border border-gray-200 hover:border-theme-300">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-3">
                <div className={`p-2 bg-gradient-to-r ${doc.color} rounded-lg mr-3`}>
                  <FileIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 group-hover:text-theme-700 transition-colors">
                    {doc.title}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    {!isExternal && (
                      <>
                        <span className="inline-block bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">
                          {doc.fileType}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 mb-3 leading-relaxed text-sm">{doc.description}</p>
            </div>
            <button
              onClick={() => isExternal ? handleExternalLink(doc.externalUrl) : handleDownload(doc.downloadUrl, doc.title)}
              className={`bg-gradient-to-r ${doc.color} text-white px-4 py-2 rounded-lg hover:shadow-md transition-all duration-300 font-medium flex items-center gap-2 text-sm ml-4`}
            >
              {isExternal ? (
                <>
                  <ExternalLink className="h-4 w-4" />
                  访问下载
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  下载文档
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    const docs = getCurrentDocs();

    if (activeCategory === 'all') {
      // 在"全部"视图中使用网格布局
      return (
        <div className="grid grid-cols-1 gap-6">
          {docs.map((doc, index) => renderDocumentCard(doc, index))}
        </div>
      );
    } else {
      // 在单个分类中使用列表布局
      return (
        <div className="space-y-4">
          {docs.map((doc, index) => renderDocumentCard(doc, index))}
        </div>
      );
    }
  };


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Compact Header */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center pt-10">
            <h1 className="text-4xl font-bold mb-3">
              文档资源下载中心
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              提供完整的产品文档、技术资料和样例代码下载服务
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-xl p-2 shadow-sm border border-gray-200">
            <div className="flex space-x-1">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-theme-600 text-white shadow-md'
                      : 'text-gray-600 hover:text-theme-600 hover:bg-gray-50'
                  }`}
                >
                  <div className="mr-2">
                    {category.icon}
                  </div>
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default Documentation;






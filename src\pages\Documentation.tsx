import React, { useState } from 'react';
import { FileText, Code, BookOpen, Download, ExternalLink, Calendar, FileIcon } from 'lucide-react';

const Documentation: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('manual');

  const categories = [
    {
      id: 'manual',
      name: '产品用户手册',
      icon: <FileText className="h-5 w-5" />,
      color: 'from-theme-600 to-theme-700',
      bgColor: 'from-theme-50 to-theme-100'
    },
    {
      id: 'technical',
      name: '技术文档',
      icon: <Code className="h-5 w-5" />,
      color: 'from-secondary-600 to-secondary-700',
      bgColor: 'from-secondary-50 to-secondary-100'
    },
    {
      id: 'examples',
      name: '样例代码',
      icon: <BookOpen className="h-5 w-5" />,
      color: 'from-theme-800 to-secondary-600',
      bgColor: 'from-warm-50 to-warm-100'
    },
    {
      id: 'downloads',
      name: '资源下载',
      icon: <Download className="h-5 w-5" />,
      color: 'from-secondary-700 to-theme-600',
      bgColor: 'from-secondary-50 to-theme-50'
    }
  ];

  const manualDocs = [
    {
      title: 'OptSuite产品用户手册',
      description: '详细介绍OptSuite的功能特性、安装配置和使用方法',
      fileType: 'PDF',
      fileSize: '15.2 MB',
      lastUpdated: '2024-01-15',
      downloadUrl: '/downloads/OptSuite-User-Manual.pdf',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    }
  ];

  const technicalDocs = [
    {
      title: 'OptSuite技术文档',
      description: '包含API接口文档、架构设计和技术规范',
      fileType: 'PDF',
      fileSize: '8.7 MB',
      lastUpdated: '2024-01-18',
      downloadUrl: '/downloads/OptSuite-Technical-Documentation.pdf',
      color: 'from-secondary-600 to-secondary-700',
      bgGradient: 'from-secondary-50 to-secondary-100'
    }
  ];

  const codeExamples = [
    {
      title: 'OptSuite样例代码',
      description: '包含Python、Java、C++等多种语言的示例代码和最佳实践',
      fileType: 'PDF',
      fileSize: '5.3 MB',
      lastUpdated: '2024-01-12',
      downloadUrl: '/downloads/OptSuite-Code-Examples.pdf',
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100'
    }
  ];

  const downloads = [
    {
      title: 'OptSuite软件下载',
      description: '最新版本的OptSuite软件安装包和相关工具',
      isExternal: true,
      externalUrl: 'https://download.optsuite.com',
      version: 'v2.1.0',
      lastUpdated: '2024-01-20',
      color: 'from-secondary-700 to-theme-600',
      bgGradient: 'from-secondary-50 to-theme-50'
    }
  ];

  const handleDownload = (url: string, filename: string) => {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExternalLink = (url: string) => {
    window.open(url, '_blank');
  };

  const getCurrentDocs = () => {
    switch (activeCategory) {
      case 'manual':
        return manualDocs;
      case 'technical':
        return technicalDocs;
      case 'examples':
        return codeExamples;
      case 'downloads':
        return downloads;
      default:
        return manualDocs;
    }
  };

  const renderDocumentCard = (doc: any, index: number) => {
    const isExternal = doc.isExternal;

    return (
      <div key={index} className="group">
        <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 transform hover:-translate-y-1">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-4">
                <div className={`p-3 bg-gradient-to-r ${doc.color} rounded-xl mr-4`}>
                  <FileIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 group-hover:text-theme-700 transition-colors">
                    {doc.title}
                  </h3>
                  <div className="flex items-center gap-2 mt-2">
                    {!isExternal && (
                      <>
                        <span className="inline-block bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">
                          {doc.fileType}
                        </span>
                        <span className="inline-block bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                          {doc.fileSize}
                        </span>
                      </>
                    )}
                    {isExternal && (
                      <span className="inline-block bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                        {doc.version}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">{doc.description}</p>
              <div className="flex items-center text-sm text-gray-500">
                <Calendar className="h-4 w-4 mr-1" />
                <span>更新时间: {doc.lastUpdated}</span>
              </div>
            </div>
            <button
              onClick={() => isExternal ? handleExternalLink(doc.externalUrl) : handleDownload(doc.downloadUrl, doc.title)}
              className={`bg-gradient-to-r ${doc.color} text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium flex items-center gap-2`}
            >
              {isExternal ? (
                <>
                  <ExternalLink className="h-4 w-4" />
                  访问下载
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  下载文档
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    const docs = getCurrentDocs();
    return (
      <div className="space-y-6">
        {docs.map((doc, index) => renderDocumentCard(doc, index))}
      </div>
    );
  };


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-white/20 shadow-lg">
              <div className="p-2 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <span className="text-secondary-200 font-medium">文档资源</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-secondary-200">文档资源</span>
              <br />
              <span className="text-white">下载中心</span>
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
            <p className="text-xl text-theme-100 max-w-3xl mx-auto leading-relaxed">
              提供完整的产品文档、技术资料和样例代码下载服务
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24 border border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">文档分类</h2>
              <nav className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                      activeCategory === category.id
                        ? `bg-gradient-to-r ${category.bgColor} text-theme-700 shadow-sm`
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <div className={`p-2 rounded-lg mr-3 ${
                      activeCategory === category.id
                        ? `bg-gradient-to-r ${category.color} text-white`
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {category.icon}
                    </div>
                    <span>{category.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Documentation;






import React from "react";
import { Link } from "react-router-dom";
import {
  TrendingUp,
  Users,
  Database,
  Target,
  Zap,
  ExternalLink,
  Check,
  Truck,
  Clock,
  MapPin,
  BarChart3,
  Globe,
  ArrowRight,
} from "lucide-react";

const WorkforceOptimizationDetail: React.FC = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        {/* 背景图片 */}
        <div className="absolute inset-0 opacity-20">
          <img
            src="https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=1200"
            alt="物流资源调度优化"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-theme-900/80 via-theme-800/60 to-secondary-900/80" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              物流端 - 资源调度优化智能决策解决方案
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              通过多资源协同与实时动态响应，实现物流全链路智能化调度优化
            </p>
          </div>
        </div>
      </section>

      {/* 痛点挑战 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">痛点挑战</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              物流资源调度优化面临的核心挑战与痛点
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 痛点1：多资源协同复杂度高 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="h-10 w-10 text-red-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                多资源协同复杂度高
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                需同时调度车辆、仓储、人力、货物等多维资源，约束条件交织（如车辆载重上限、仓库容量、司机工时法规），人工调度顾此失彼，资源利用率低。
              </p>
            </div>

            {/* 痛点2：动态扰动实时响应难 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-orange-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-10 w-10 text-orange-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                动态扰动实时响应难
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                交通拥堵、订单取消、车辆故障等突发情况需分钟级重调度，人工决策滞后。
              </p>
            </div>

            {/* 痛点3：成本与时效难以平衡 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-yellow-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-10 w-10 text-yellow-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                成本与时效难以平衡
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                降低运输成本可能延长时效，而追求极速达会推高成本。
              </p>
            </div>

            {/* 痛点4："最后一公里"效率瓶颈 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                "最后一公里"效率瓶颈
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                城市配送路网复杂、停靠点分散、客户时间窗严苛，传统路径规划失效。
              </p>
            </div>

            {/* 痛点5：全局优化与局部决策冲突 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-purple-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="h-10 w-10 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                全局优化与局部决策冲突
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                区域仓库为降低库存成本减少备货，导致跨仓调拨频次增加。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 目标价值 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">目标价值</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              通过智能化技术实现物流资源调度优化的核心价值
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 多资源协同与动态响应 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-green-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Database className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                多资源协同与动态响应
              </h3>
              <p className="text-gray-600 text-center mb-6">
                智能化资源调度网络
              </p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    根据历史调度策略，动态响应订单波动
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    根据实时数据，滚动更新未来1小时调度方案
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    同时调度车辆、仓储、人力、货物等多维资源
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    分钟级响应突发情况，自动重调度
                  </span>
                </div>
              </div>
            </div>

            {/* 多目标平衡与全局优化 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                多目标平衡与全局优化
              </h3>
              <p className="text-gray-600 text-center mb-6">智能平衡多重目标</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    构建目标函数关于成本（燃油/人力）、时效（平均配送时长）、体验（准时率）等因素
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    管理者按策略选择方案，量化决策依据
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    为仓库、车辆、分拨中心设计独立智能体
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    通过协作博弈实现全局最优
                  </span>
                </div>
              </div>
            </div>

            {/* 最后一公里智能优化 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-purple-100">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                最后一公里智能优化
              </h3>
              <p className="text-gray-600 text-center mb-6">精准配送路径规划</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">融合路网拓扑、实时交通流量、历史规律</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    动态生成抗拥堵的个性化路径
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">应对城市配送路网复杂、停靠点分散挑战</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">满足客户时间窗严苛要求</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 应用案例 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">应用案例</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示物流资源调度优化在复杂场景中的卓越表现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 案例1：石化企业多式联运 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="石化企业多式联运优化"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    多式联运
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某石化企业物流多式联运优化项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  多式联运
                </p>
                <div className="mb-6 h-52">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    通过建立整体路径优化模型，实现多式联运最小运输成本的目标。优化后在10分钟内找到全局最优解或次优解，且成本优于人工解1%以上；降低调度人员工作量，节省调度计划编制时间1到3倍以上；降低运输成本，预计节省总运输成本5%以上。
                  </p>
                </div>

                <Link
                  to="/cases?case=petrochemical-multimodal-transport"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例2：复杂容量限制车辆路径规划 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="车辆路径规划优化"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    路径规划
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某复杂容量限制车辆路径规划项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  路径规划
                </p>
                <div className="mb-6 h-52">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    针对复杂容量限制车辆路径规划，研究高效求解时空耦合约束下的超大规模资源调度求解问题的强化学习和学习优化计算求解框架，可以有效处理变量个数十万至百万规模的车辆路径规划问题，在Solomon等数据集上速度可以领先整数规划的分支定界法和割平面法50%。开发了定制的增广拉格朗日函数方法，采用块坐标下降法将复杂的全局耦合约束问题分解为多个可并行求解的子问题，并给出了算法的收敛性和最优性保障。
                  </p>
                </div>

                <Link
                  to="/cases?case=complex-vehicle-routing"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例3：物流企业航运板箱配载 */}
            <div className="flex flex-col bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="航运板箱配载优化"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    配载装箱
                  </div>
                </div>
              </div>
              <div className="p-6 flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某物流企业航运板箱配载项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">配载装箱</p>
                <div className="mb-6 h-52">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    同时制定航班计划和配载计划的两阶段决策问题，使用主模型与子模型联合求解的方案，结合列生成算法和最短路模型进行求解，以期实现两阶段问题的总体最优。
                  </p>
                </div>

                <Link
                  to="/cases?case=aviation-cargo-loading"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
  );
};

export default WorkforceOptimizationDetail;

import React from 'react';
import { usePara<PERSON>, <PERSON> } from 'react-router-dom';
import SupplyChainOptimizationDetail from '../components/solutions/SupplyChainOptimizationDetail';
import SmartSchedulingDetail from '../components/solutions/SmartSchedulingDetail';
import WorkforceOptimizationDetail from '../components/solutions/WorkforceOptimizationDetail';
import IndustryCasesDetail from '@/components/solutions/IndustryCasesDetail';

const SolutionDetail: React.FC = () => {
  const { solutionId } = useParams<{ solutionId: string }>();

  // 根据解决方案ID渲染对应的组件
  const renderSolutionComponent = () => {
    switch (solutionId) {
      case 'supply-chain-optimization':
        return <SupplyChainOptimizationDetail />;
      case 'smart-scheduling':
        return <SmartSchedulingDetail />;
      case 'workforce-optimization':
        return <WorkforceOptimizationDetail />;
      default:
        return <IndustryCasesDetail />;
    }
  };

  return renderSolutionComponent();
};

export default SolutionDetail;

import React, { useState } from "react";
import { Link } from "react-router-dom";
import {
  ExternalLink,
  Train,
  Plane,
  Factory,
} from "lucide-react";

interface CaseItem {
  id: string;
  title: string;
  keyword: string;
  image: string;
  painPoints: string;
  solution: string;
}

interface IndustryCasesSectionProps {
  showHeader?: boolean;
  className?: string;
}

const IndustryCasesSection: React.FC<IndustryCasesSectionProps> = ({ 
  showHeader = true, 
  className = "" 
}) => {
  const [activeTab, setActiveTab] = useState("rail");

  const tabs = [
    { id: "rail", name: "轨道交通", icon: Train },
    { id: "aerospace", name: "航空航天", icon: Plane },
    { id: "steel", name: "钢铁", icon: Factory },
  ];

  const cases: Record<string, CaseItem[]> = {
    rail: [
      {
        id: "beijing-shanghai-rail",
        title: "京沪高铁排程优化算法项目",
        keyword: "高铁排班",
        image: "https://images.pexels.com/photos/544966/pexels-photo-544966.jpeg?auto=compress&cs=tinysrgb&w=800",
        painPoints: "现状：主要技术手段是\"人工经验+计算机显示\" 挑战：（1）问题复杂且规模巨大，（JSP模型）京沪高铁典型场景变量约205万，约束约3600万 （2）传统分支定界等算法不能满足运行图编制时效性和最优性需求 （3）人工智能赋能的求解方法是攻克这一难题的关键",
        solution: "通过建立超大规模混合整数规划，最大化开行列车数量（或者最小化运营成本），考虑约束停站方案、运行时间、列车间隔、区间越行、动车交路、进路占用、股道占用等，考虑变量列车次序、资源占用时间等，实现整体求解效率超过人工排班40倍。"
      }
    ],
    aerospace: [
      {
        id: "spacecraft-landing",
        title: "航天器软着陆轨迹优化项目",
        keyword: "轨迹优化",
        image: "https://images.pexels.com/photos/586063/pexels-photo-586063.jpeg?auto=compress&cs=tinysrgb&w=800",
        painPoints: "航天器软着陆轨迹优化是实现着陆任务成功与否的关键，在着陆过程中会受到各种动力学影响，如引力、大气阻力、风速等，这些都需要在轨迹规划中考虑。优化过程涉及到的SOCP问题需要使用求解器进行求解。实现目标是：（1）国产自主可控，具有高度定制性：为避免\"卡脖子\"风险，国产自主可控，高度定制化的求解器需求。（2）满足求解效率：环境的不确定性需要通过预先的探测数据来预估并在轨迹规划中考虑，也对轨迹求解的效率有着较高要求。（3）精准保证：软着陆轨迹规划要求极高的精度以确保安全着陆，任何轨迹偏差都可能导致与预期着陆区域的偏离，甚至可能发生碰撞。",
        solution: "相比现有技术，求解效率显著提升求解软着陆轨迹问题的速度；优化过程中保持或提升轨迹计算的精度，确保软着陆过程的安全和可靠；开发的算法和模型能够满足实时计算的需求，适用于动态变化的航天任务环境中。"
      }
    ],
    steel: [
      {
        id: "steel-ore-blending",
        title: "某钢铁研究院配矿优化项目",
        keyword: "配矿优化",
        image: "https://images.pexels.com/photos/236705/pexels-photo-236705.jpeg?auto=compress&cs=tinysrgb&w=800",
        painPoints: "现今钢铁企业在配矿环节，大都采用人工配矿的方式，主要存在以下问题：（1）经验不一：数据处理效率低、可能因操作人员的经验、技能和主观判断而导致配矿结果的不一致性；（2）市场变化：市场条件和原料成本经常变化，人工作业在快速响应这些变化方面可能会有所不足、难以最优化原料成本；（3）数据不足：一体化配矿研究受限于数据量不足、数据差异大等情况，导致可用性差，应用价值有待提升",
        solution: "基于多目标、多约束、长流程、强耦合、高复杂度的非线性、非凸问题的多层级配矿计划产品方案。算法充分考虑影响因素包括矿粉种类、入炉品位、熟料率、熔剂消耗、有害元素等多个因素对高炉焦比、产量和质量的影响；同时兼顾了矿粉的最大消耗量（供应量）、清库存、转运亏吨率等实际情况；通过机理参数的自主设定，确保了配矿方案不仅高效、节能，而且紧密贴合实际生产需求。"
      }
    ]
  };

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showHeader && (
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">行业应用案例</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示智能优化技术在不同行业中的卓越表现
            </p>
          </div>
        )}

        {/* Tab导航 */}
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-xl p-2 shadow-lg border border-gray-200">
            <div className="flex space-x-2">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-theme-600 text-white shadow-md"
                        : "text-gray-600 hover:text-theme-600 hover:bg-gray-50"
                    }`}
                  >
                    <IconComponent className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* 案例内容 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {cases[activeTab].map((caseItem) => (
            <div key={caseItem.id} className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src={caseItem.image}
                  alt={caseItem.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    {caseItem.keyword}
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {caseItem.title}
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  {caseItem.keyword}
                </p>
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">客户痛点：</h4>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                    {caseItem.painPoints}
                  </p>
                </div>
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">赋能方案：</h4>
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                    {caseItem.solution}
                  </p>
                </div>

                <Link
                  to={`/cases?case=${caseItem.id}`}
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* 如果当前分类没有案例，显示占位内容 */}
        {cases[activeTab].length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="text-gray-400 text-2xl">🚧</div>
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">敬请期待</h3>
            <p className="text-gray-500">该分类下的案例正在整理中，即将上线</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default IndustryCasesSection;

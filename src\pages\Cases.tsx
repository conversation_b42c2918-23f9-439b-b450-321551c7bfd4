import React, { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import {
  Factory,
  Settings,
  Target,
  Lightbulb,
  Warehouse,
  TrendingUp,
  Wrench,
  Truck,
  Route,
  Package,
  Train,
  Layers
} from "lucide-react";

const Cases: React.FC = () => {
  const location = useLocation();
  const caseRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // 从URL参数获取要跳转的案例ID
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const caseId = params.get("case");
    if (caseId && caseRefs.current[caseId]) {
      setTimeout(() => {
        caseRefs.current[caseId]?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }, [location]);

  const cases = [
    {
      id: "precision-parts",
      title: "某精密仪器零部件智能排产项目",
      keywords: ["精细化制造排产"],
      category: "精密制造",
      icon: <Factory className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        'OptAPS助力精密仪器零部件企业自主工厂生产制造，研发实时动态排产优化模型与算法，在排产效能上大幅领先于传统人工方法，实现了秒级的排产响应速度，显著提升了设备利用率、订单交付准时率、排产效率以及成本管理，有效地解决了在应对生产过程中的"人、机、料、法、环、测"等方面所遇到的动态决策难题。',
      painPoints: [
        {
          title: "（1）机床程序高度依赖工艺工程师群体：",
          content:
            "CNC行业高度分散 CR8 < 5%，但规模化瓶颈单一；工艺工程师培养周期10-15年，现平均年龄45岁；机床和工艺人员配比1比1，行业需求大。",
        },
        {
          title: "（2）大量复杂的排产场景人工难以计算：",
          content:
            "每个月有1000多种零件需要排产，生产设备机台多达560+台，从大尺寸零件到精密5轴加工零件均可加工，排产极具多品种小批量的复杂性。人机匹配的问题需要人工支持，新的产品或订单加进来后干扰当前订单的延误，靠人脑经验来调度计划，造成交付延期。",
        },
      ],
      solution:
        '研发实时动态排产优化模型与算法，在排产效能上大幅领先于传统人工方法，实现了秒级的排产响应速度，显著提升了设备利用率、订单交付准时率、排产效率以及成本管理，有效地解决了在应对生产过程中的"人、机、料、法、环、测"等方面所遇到的动态决策难题。',
    },
    {
      id: "mold-processing",
      title: "某模具加工智能排产优化项目",
      keywords: ["大规模模具排产"],
      category: "模具制造",
      icon: <Settings className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "OptAPS助力大规模精加工模具生产制造的快速排产，解决复杂的生产线设置和多变的订单需求调度问题、高精度要求和多工序交叉的工作流程瓶颈、优化生产效率和资源分配，实现求解效率提升。",
      painPoints: [
        {
          title: "（1）问题规模大、业务逻辑复杂：",
          content:
            "日常需要排产量在1500个订单以上，峰值可达4000个订单；组立/非组立，同批次加工，电极约束，资源约束，设备休息时间...",
        },
        {
          title: "（2）求解结果质量、效率要求高：",
          content:
            "多目标优化场景下希望将各目标优化至10%gap以内；希望在20min以内，满足求解质量的情况下求解如此大规模问题。",
        },
      ],
      solution:
        "基于工艺路线及资源池，将工序直接排配到具体的设备、人员及加工时间。求解GAP由原来的90%提升至0-20%。日常1500+订单可获得最优解；极限压测数据gap约20%；在3-20分钟时间内可获得高质量解。日常任务可在3分钟内获得最优解。极限压测数用时约20分钟。",
    },
    {
      id: "spacecraft-landing",
      title: "航天器软着陆轨迹优化项目",
      keywords: ["轨迹优化"],
      category: "航天应用",
      icon: <Target className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/586063/pexels-photo-586063.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "OptSuite成功应用于航天器软着陆轨迹优化，通过高性能数学规划求解器，实现了轨迹规划的精确计算和实时优化，确保航天器安全着陆。该项目展现了OptSuite在复杂约束优化问题中的卓越性能。",
      painPoints: [
        {
          title: "（1）国产自主可控，具有高度定制性：",
          content: '为避免"卡脖子"风险，国产自主可控，高度定制化的求解器需求。',
        },
        {
          title: "（2）满足求解效率：",
          content:
            "环境的不确定性需要通过预先的探测数据来预估并在轨迹规划中考虑，也对轨迹求解的效率有着较高要求。",
        },
        {
          title: "（3）精准保证：",
          content:
            "软着陆轨迹规划要求极高的精度以确保安全着陆，任何轨迹偏差都可能导致与预期着陆区域的偏离，甚至可能发生碰撞。",
        },
      ],
      solution:
        "相比现有技术，求解效率显著提升求解软着陆轨迹问题的速度；优化过程中保持或提升轨迹计算的精度，确保软着陆过程的安全和可靠；开发的算法和模型能够满足实时计算的需求，适用于动态变化的航天任务环境中。",
    },
    {
      id: "paper-warehouse-coordination",
      title: "某纸制品加工仓储配载协同项目",
      keywords: ["仓储协同优化"],
      category: "仓储物流",
      icon: <Warehouse className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/4481259/pexels-photo-4481259.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "通过数据仿真，进行生产下线、选择存储缓存区、规划RGV小车路径、决定板链运输方向、进行配载发运的全过程，设置基于整个工厂的模拟结果选择的最优决策，以实现配载发运区和缓存区WIP协同无阻塞。",
      painPoints: [
        {
          title: "（1）发运计划未与生产计划联动：",
          content:
            "三级缓存被动调动，导致缓存区阻塞，严重可导致生产停线。",
        },
        {
          title: "（2）人工配载发运单效率低：",
          content:
            "配载涉及客户线路合并、大小单合并、车辆调配、容量限制、司机调度等各方面因素，尤其是生产情况的考虑，人工配载无法全局考量。",
        },
      ],
      solution:
        "通过数据仿真，进行生产下线、选择存储缓存区、规划RGV小车路径、决定板链运输方向、进行配载发运的全过程，设置基于整个工厂的模拟结果选择的最优决策，以实现配载发运区和缓存区WIP协同无阻塞。",
    },
    {
      id: "telecom-demand-forecast",
      title: "某通信集团智家产品销量预测项目",
      keywords: ["需求预测"],
      category: "需求预测",
      icon: <TrendingUp className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/590022/pexels-photo-590022.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "为解决以光猫、机顶盒为典型代表的存货库存高企难题，某通信集团供应链管理部使用AI需求预测、替代人为经验需求预测。通过引入算法对市场营销物资光猫和机顶盒的需求进行预测，实现了自动化生成申购单，从源头对需求进行有效管控，基本做到了采购和需求的供需平衡，极大降低了采购压力和库存压力。",
      painPoints: [
        {
          title: "（1）市场营销物资需求依赖于各级管理者个人经验：",
          content:
            "导致各级决策层级难以辨识需求合理性，导致公司总存货成本高企。",
        },
        {
          title: "（2）体系架构陈旧：",
          content:
            "省、市、县区传统供应链路径\"流程长、无效囤积多\"导致总体营销资源周转速度低、效益低。",
        },
        {
          title: "（3）基层工作量巨大：",
          content:
            "工作效率不适应数字化资源管理要求。",
        },
      ],
      solution:
        "为解决以光猫、机顶盒为典型代表的存货库存高企难题，某通信集团供应链管理部使用AI需求预测、替代人为经验需求预测。通过引入算法对市场营销物资光猫和机顶盒的需求进行预测，实现了自动化生成申购单，从源头对需求进行有效管控，基本做到了采购和需求的供需平衡，极大降低了采购压力和库存压力。",
    },
    {
      id: "metal-processing-optimization",
      title: "某金属加工企业工艺优化项目",
      keywords: ["工艺优化"],
      category: "工艺优化",
      icon: <Wrench className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "综合考虑目前所有箔轧库存、在制品库存、订单规格需求，在5-10分钟内快速获得全局最优分切方案，提升分切计划制定效率的同时最大化提升箔轧的成材率。",
      painPoints: [
        {
          title: "（1）切割组合方式多样，金属原料利用率不高：",
          content:
            "下游客户对于金属薄片的产品尺寸要求各异，产品规格多，大量订单结合多种规格的组合方式达到百万种，组合方式直接决定原材料成本。",
        },
        {
          title: "（2）分切、退火工艺不能有效联动：",
          content:
            "分切完的半成品需要较长的等待时间，才能将退火炉装满。但是若未装满退火炉，就有可能造成能源的浪费。一方面是等待和库存高的周期成本，另一方面是生产成本，对于此类复杂约束的问题无法全局考虑。另外，各产品的退火时长不同，同一订单可能不会在相同时间出炉进行交付。",
        },
      ],
      solution:
        "综合考虑目前所有箔轧库存、在制品库存、订单规格需求，在5-10分钟内快速获得全局最优分切方案，提升分切计划制定效率的同时最大化提升箔轧的成材率。优化分切先后顺序，减少分切和退火之间的时间停顿，获得最优分切计划；优化分切后的箔轧块装炉方案，获得最优装炉计划。",
    },
    {
      id: "petrochemical-multimodal-transport",
      title: "某石化企业物流多式联运优化项目",
      keywords: ["多式联运"],
      category: "物流运输",
      icon: <Truck className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "通过建立整体路径优化模型，实现多式联运最小运输成本的目标。优化后在10分钟内找到全局最优解或次优解，且成本优于人工解1%以上；降低调度人员工作量，节省调度计划编制时间1到3倍以上；降低运输成本，预计节省总运输成本5%以上。",
      painPoints: [
        {
          title: "（1）标准规则不统一：",
          content:
            "不同的运输方式对管理要求不同，完成一个完整的运输链，需在不同的运输方式之间多次重复录入大量信息；对货物品类划分不同，对应的安全标准要求也各不相同。",
        },
        {
          title: "（2）信息联通不足：",
          content:
            "多式联运信息互联共享机制不健全，铁路、公路、水运、民航等企业信息开放程度不一，很难提供全程一体化物流服务，对于供应链全程\"不断链\"的整体掌控能力不够。",
        },
        {
          title: "（3）运输成本高：",
          content:
            "涉及多种运输方式的组合运输，成本差异大，人工计算难以获取最优成本。",
        },
      ],
      solution:
        "通过建立整体路径优化模型，实现多式联运最小运输成本的目标。优化后在10分钟内找到全局最优解或次优解，且成本优于人工解1%以上；降低调度人员工作量，节省调度计划编制时间1到3倍以上；降低运输成本，预计节省总运输成本5%以上。",
    },
    {
      id: "complex-vehicle-routing",
      title: "某复杂容量限制车辆路径规划项目",
      keywords: ["路径规划"],
      category: "路径规划",
      icon: <Route className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "针对复杂容量限制车辆路径规划，研究高效求解时空耦合约束下的超大规模资源调度求解问题的强化学习和学习优化计算求解框架，可以有效处理变量个数十万至百万规模的车辆路径规划问题。",
      painPoints: [],
      solution:
        "针对复杂容量限制车辆路径规划，研究高效求解时空耦合约束下的超大规模资源调度求解问题的强化学习和学习优化计算求解框架，可以有效处理变量个数十万至百万规模的车辆路径规划问题，在Solomon等数据集上速度可以领先整数规划的分支定界法和割平面法50%。开发了定制的增广拉格朗日函数方法，采用块坐标下降法将复杂的全局耦合约束问题分解为多个可并行求解的子问题，并给出了算法的收敛性和最优性保障。",
    },
    {
      id: "aviation-cargo-loading",
      title: "某物流企业航运板箱配载项目",
      keywords: ["配载装箱"],
      category: "配载装箱",
      icon: <Package className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/906494/pexels-photo-906494.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "同时制定航班计划和配载计划的两阶段决策问题，使用主模型与子模型联合求解的方案，结合列生成算法和最短路模型进行求解，以期实现两阶段问题的总体最优。",
      painPoints: [
        {
          title: "（1）现有板箱配载计划成本高：",
          content:
            "根据现有运力资源和服务网络，企业成本偏高，货物装箱率不高，配载分配不均匀等问题明显。",
        },
        {
          title: "（2）航班计划未结合航班时刻表：",
          content:
            "前期的航班时刻表未作为输入数据，因此需要根据已有的逐条航班数据，将其拼接为完整航线，并将航线分配给匹配的机型，同时还需要决策每一条航班的起飞时间。之后在此基础上进行板箱配载计划。考虑航班计划的板箱配载问题属于一个两阶段决策问题，第一阶段为战术层的航班计划，第二阶段为运营层的配载计划。",
        },
      ],
      solution:
        "同时制定航班计划和配载计划的两阶段决策问题，使用主模型与子模型联合求解的方案，结合列生成算法和最短路模型进行求解，以期实现两阶段问题的总体最优。",
    },
    {
      id: "beijing-shanghai-rail",
      title: "京沪高铁排程优化算法项目",
      keywords: ["高铁排班"],
      category: "轨道交通",
      icon: <Train className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/544966/pexels-photo-544966.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "通过建立超大规模混合整数规划，最大化开行列车数量（或者最小化运营成本），考虑约束停站方案、运行时间、列车间隔、区间越行、动车交路、进路占用、股道占用等，考虑变量列车次序、资源占用时间等，实现整体求解效率超过人工排班40倍。",
      painPoints: [
        {
          title: "（1）问题复杂且规模巨大：",
          content:
            "（JSP模型）京沪高铁典型场景变量约205万，约束约3600万。",
        },
        {
          title: "（2）传统分支定界等算法不能满足运行图编制时效性和最优性需求：",
          content:
            "传统算法无法在合理时间内找到高质量解。",
        },
        {
          title: "（3）人工智能赋能的求解方法是攻克这一难题的关键：",
          content:
            "需要采用先进的AI算法来解决超大规模优化问题。",
        },
      ],
      solution:
        "通过建立超大规模混合整数规划，最大化开行列车数量（或者最小化运营成本），考虑约束停站方案、运行时间、列车间隔、区间越行、动车交路、进路占用、股道占用等，考虑变量列车次序、资源占用时间等，实现整体求解效率超过人工排班40倍。",
    },
    {
      id: "steel-ore-blending",
      title: "某钢铁研究院配矿优化项目",
      keywords: ["配矿优化"],
      category: "钢铁冶金",
      icon: <Layers className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/236705/pexels-photo-236705.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "基于多目标、多约束、长流程、强耦合、高复杂度的非线性、非凸问题的多层级配矿计划产品方案。算法充分考虑影响因素包括矿粉种类、入炉品位、熟料率、熔剂消耗、有害元素等多个因素对高炉焦比、产量和质量的影响。",
      painPoints: [
        {
          title: "（1）经验不一：",
          content:
            "数据处理效率低、可能因操作人员的经验、技能和主观判断而导致配矿结果的不一致性。",
        },
        {
          title: "（2）市场变化：",
          content:
            "市场条件和原料成本经常变化，人工作业在快速响应这些变化方面可能会有所不足、难以最优化原料成本。",
        },
        {
          title: "（3）数据不足：",
          content:
            "一体化配矿研究受限于数据量不足、数据差异大等情况，导致可用性差，应用价值有待提升。",
        },
      ],
      solution:
        "基于多目标、多约束、长流程、强耦合、高复杂度的非线性、非凸问题的多层级配矿计划产品方案。算法充分考虑影响因素包括矿粉种类、入炉品位、熟料率、熔剂消耗、有害元素等多个因素对高炉焦比、产量和质量的影响；同时兼顾了矿粉的最大消耗量（供应量）、清库存、转运亏吨率等实际情况；通过机理参数的自主设定，确保了配矿方案不仅高效、节能，而且紧密贴合实际生产需求。",
    },
  ];

  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">成功案例</h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              真实案例展示我们产品在各行业中的卓越表现和价值创造
            </p>
          </div>
        </div>
      </section>

      {/* 案例详情 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {cases.map((caseItem) => (
              <div
                key={caseItem.id}
                ref={(el) => (caseRefs.current[caseItem.id] = el)}
                className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100"
              >
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-0">
                  <div className="relative w-full h-full">
                    <img
                      src={caseItem.image}
                      alt={caseItem.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                        {caseItem.icon}
                        <span className="ml-2">{caseItem.category}</span>
                      </div>
                    </div>
                  </div>

                  {/* 右侧详细内容 - 占3列 */}
                  <div className="lg:col-span-3 p-6">
                    {/* 标题 */}
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        {caseItem.title}
                      </h3>
                      {/* 场景关键词 */}
                      {caseItem.keywords && (
                        <div className="flex flex-wrap gap-2">
                          {caseItem.keywords.map((keyword, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-1 bg-theme-100 text-theme-700 text-sm font-medium rounded-full border border-theme-200"
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* 内容区域 - 使用网格布局优化空间利用 */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                      {/* 客户痛点 */}
                      <div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <Lightbulb className="h-5 w-5 mr-2 text-theme-600" />
                          客户痛点
                        </h4>
                        <p className="text-gray-600 leading-relaxed mb-4 text-sm">
                          {caseItem.id === "precision-parts" &&
                            "企业聚焦于定制化柔性生产解决方案，支持产品快速打样需求，生产过程高度依赖工艺工程师、生管计划员等角色，导致生产存在瓶颈、产能效率难以提升，主要痛点表现在："}
                          {caseItem.id === "mold-processing" &&
                            "大规模精加工模具生产制造业务模式依赖于自动化和精密工艺的集成应用，重视批量生产与个性化定制的平衡。该企业在生产排产排程方面，主要痛点包括复杂的生产线设置和多变的订单需求导致的调度困难，高精度要求和多工序交叉的工作流程容易引发生产瓶颈，以及在保证交货期的压力下，优化生产效率和资源分配的挑战，具体体现为："}
                          {caseItem.id === "spacecraft-landing" &&
                            "航天器软着陆轨迹优化是实现着陆任务成功与否的关键，在着陆过程中会受到各种动力学影响，如引力、大气阻力、风速等，这些都需要在轨迹规划中考虑。优化过程涉及到的SOCP问题需要使用求解器进行求解。实现目标是："}
                          {caseItem.id === "paper-warehouse-coordination" &&
                            "某纸制品加工企业当前纸板产线的生产区、一级缓存区、二级缓存区、三级缓存区、配载发运区存在联动不顺畅问题，APS和TMS系统的协同性不佳，主要痛点如下："}
                          {caseItem.id === "telecom-demand-forecast" &&
                            "某通信集团智家产品管理存在\"库存规模大、周转速度慢、需求不准、体系陈旧、基层压力难以化解\"问题。根据全流程观察，主要痛点表现在："}
                          {caseItem.id === "metal-processing-optimization" &&
                            "某金属加工企业，通过将金属原料进行延压、分切、退火等工艺后，加工成金属薄片。长达1个月的交付周期和高达2：1的存销比成为了困扰企业的最大问题，具体表现在："}
                          {caseItem.id === "petrochemical-multimodal-transport" &&
                            "某石化企业有着石油化工品、润滑油、炼油品的特殊性，物流运输方式通常采用多式联运模式，即货品从A到B点需要通过铁路、水路、航空等不同方式组合运输，主要痛点表现在："}
                          {caseItem.id === "aviation-cargo-loading" &&
                            "某物流企业在航空货物运输方面想寻求新的管理运营方案，并对现有的板箱配载计划和航空货运网络规划进行优化，帮助企业实现降本增效，主要问题表现在："}
                          {caseItem.id === "beijing-shanghai-rail" &&
                            "现状：主要技术手段是\"人工经验+计算机显示\" 挑战："}
                          {caseItem.id === "steel-ore-blending" &&
                            "现今钢铁企业在配矿环节，大都采用人工配矿的方式，主要存在以下问题："}
                        </p>
                        {caseItem.painPoints.length > 0 && (
                          <div className="space-y-3">
                            {caseItem.painPoints.map((point, index) => (
                              <div
                                key={index}
                                className="bg-gray-50 rounded-lg p-3"
                              >
                                <h5 className="font-semibold text-gray-900 mb-1 text-sm">
                                  {point.title}
                                </h5>
                                <p className="text-gray-600 text-xs leading-relaxed">
                                  {point.content}
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* 赋能方案 */}
                      <div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <Target className="h-5 w-5 mr-2 text-theme-600" />
                          赋能方案
                        </h4>
                        <div className="bg-theme-50 rounded-lg p-4">
                          <p className="text-gray-700 leading-relaxed text-sm">
                            {caseItem.solution}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Cases;

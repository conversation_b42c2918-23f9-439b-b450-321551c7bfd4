import React, { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { Factory, Settings, Target, Lightbulb } from "lucide-react";

const Cases: React.FC = () => {
  const location = useLocation();
  const caseRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // 从URL参数获取要跳转的案例ID
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const caseId = params.get("case");
    if (caseId && caseRefs.current[caseId]) {
      setTimeout(() => {
        caseRefs.current[caseId]?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }, [location]);

  const cases = [
    {
      id: "precision-parts",
      title: "某精密仪器零部件智能排产项目",
      keywords: ["精细化制造排产"],
      category: "精密制造",
      icon: <Factory className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        'OptAPS助力精密仪器零部件企业自主工厂生产制造，研发实时动态排产优化模型与算法，在排产效能上大幅领先于传统人工方法，实现了秒级的排产响应速度，显著提升了设备利用率、订单交付准时率、排产效率以及成本管理，有效地解决了在应对生产过程中的"人、机、料、法、环、测"等方面所遇到的动态决策难题。',
      painPoints: [
        {
          title: "（1）机床程序高度依赖工艺工程师群体：",
          content:
            "CNC行业高度分散 CR8 < 5%，但规模化瓶颈单一；工艺工程师培养周期10-15年，现平均年龄45岁；机床和工艺人员配比1比1，行业需求大。",
        },
        {
          title: "（2）大量复杂的排产场景人工难以计算：",
          content:
            "每个月有1000多种零件需要排产，生产设备机台多达560+台，从大尺寸零件到精密5轴加工零件均可加工，排产极具多品种小批量的复杂性。人机匹配的问题需要人工支持，新的产品或订单加进来后干扰当前订单的延误，靠人脑经验来调度计划，造成交付延期。",
        },
      ],
      solution:
        '研发实时动态排产优化模型与算法，在排产效能上大幅领先于传统人工方法，实现了秒级的排产响应速度，显著提升了设备利用率、订单交付准时率、排产效率以及成本管理，有效地解决了在应对生产过程中的"人、机、料、法、环、测"等方面所遇到的动态决策难题。',
    },
    {
      id: "mold-processing",
      title: "某模具加工智能排产优化项目",
      keywords: ["大规模模具排产"],
      category: "模具制造",
      icon: <Settings className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "OptAPS助力大规模精加工模具生产制造的快速排产，解决复杂的生产线设置和多变的订单需求调度问题、高精度要求和多工序交叉的工作流程瓶颈、优化生产效率和资源分配，实现求解效率提升。",
      painPoints: [
        {
          title: "（1）问题规模大、业务逻辑复杂：",
          content:
            "日常需要排产量在1500个订单以上，峰值可达4000个订单；组立/非组立，同批次加工，电极约束，资源约束，设备休息时间...",
        },
        {
          title: "（2）求解结果质量、效率要求高：",
          content:
            "多目标优化场景下希望将各目标优化至10%gap以内；希望在20min以内，满足求解质量的情况下求解如此大规模问题。",
        },
      ],
      solution:
        "基于工艺路线及资源池，将工序直接排配到具体的设备、人员及加工时间。求解GAP由原来的90%提升至0-20%。日常1500+订单可获得最优解；极限压测数据gap约20%；在3-20分钟时间内可获得高质量解。日常任务可在3分钟内获得最优解。极限压测数用时约20分钟。",
    },
    {
      id: "spacecraft-landing",
      title: "航天器软着陆轨迹优化项目",
      keywords: ["轨迹优化"],
      category: "航天应用",
      icon: <Target className="h-4 w-4" />,
      image:
        "https://images.pexels.com/photos/586063/pexels-photo-586063.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "OptSuite成功应用于航天器软着陆轨迹优化，通过高性能数学规划求解器，实现了轨迹规划的精确计算和实时优化，确保航天器安全着陆。该项目展现了OptSuite在复杂约束优化问题中的卓越性能。",
      painPoints: [
        {
          title: "（1）国产自主可控，具有高度定制性：",
          content: '为避免"卡脖子"风险，国产自主可控，高度定制化的求解器需求。',
        },
        {
          title: "（2）满足求解效率：",
          content:
            "环境的不确定性需要通过预先的探测数据来预估并在轨迹规划中考虑，也对轨迹求解的效率有着较高要求。",
        },
        {
          title: "（3）精准保证：",
          content:
            "软着陆轨迹规划要求极高的精度以确保安全着陆，任何轨迹偏差都可能导致与预期着陆区域的偏离，甚至可能发生碰撞。",
        },
      ],
      solution:
        "相比现有技术，求解效率显著提升求解软着陆轨迹问题的速度；优化过程中保持或提升轨迹计算的精度，确保软着陆过程的安全和可靠；开发的算法和模型能够满足实时计算的需求，适用于动态变化的航天任务环境中。",
    },
  ];

  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">成功案例</h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              真实案例展示我们产品在各行业中的卓越表现和价值创造
            </p>
          </div>
        </div>
      </section>

      {/* 案例详情 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {cases.map((caseItem) => (
              <div
                key={caseItem.id}
                ref={(el) => (caseRefs.current[caseItem.id] = el)}
                className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100"
              >
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-0">
                  <div className="relative w-full h-full">
                    <img
                      src={caseItem.image}
                      alt={caseItem.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                        {caseItem.icon}
                        <span className="ml-2">{caseItem.category}</span>
                      </div>
                    </div>
                  </div>

                  {/* 右侧详细内容 - 占3列 */}
                  <div className="lg:col-span-3 p-6">
                    {/* 标题 */}
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        {caseItem.title}
                      </h3>
                      {/* 场景关键词 */}
                      {caseItem.keywords && (
                        <div className="flex flex-wrap gap-2">
                          {caseItem.keywords.map((keyword, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-1 bg-theme-100 text-theme-700 text-sm font-medium rounded-full border border-theme-200"
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* 内容区域 - 使用网格布局优化空间利用 */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                      {/* 客户痛点 */}
                      <div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <Lightbulb className="h-5 w-5 mr-2 text-theme-600" />
                          客户痛点
                        </h4>
                        <p className="text-gray-600 leading-relaxed mb-4 text-sm">
                          {caseItem.id === "precision-parts" &&
                            "企业聚焦于定制化柔性生产解决方案，支持产品快速打样需求，生产过程高度依赖工艺工程师、生管计划员等角色，导致生产存在瓶颈、产能效率难以提升，主要痛点表现在："}
                          {caseItem.id === "mold-processing" &&
                            "大规模精加工模具生产制造业务模式依赖于自动化和精密工艺的集成应用，重视批量生产与个性化定制的平衡。该企业在生产排产排程方面，主要痛点包括复杂的生产线设置和多变的订单需求导致的调度困难，高精度要求和多工序交叉的工作流程容易引发生产瓶颈，以及在保证交货期的压力下，优化生产效率和资源分配的挑战，具体体现为："}
                          {caseItem.id === "spacecraft-landing" &&
                            "航天器软着陆轨迹优化是实现着陆任务成功与否的关键，在着陆过程中会受到各种动力学影响，如引力、大气阻力、风速等，这些都需要在轨迹规划中考虑。优化过程涉及到的SOCP问题需要使用求解器进行求解。实现目标是："}
                        </p>
                        <div className="space-y-3">
                          {caseItem.painPoints.map((point, index) => (
                            <div
                              key={index}
                              className="bg-gray-50 rounded-lg p-3"
                            >
                              <h5 className="font-semibold text-gray-900 mb-1 text-sm">
                                {point.title}
                              </h5>
                              <p className="text-gray-600 text-xs leading-relaxed">
                                {point.content}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 赋能方案 */}
                      <div>
                        <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <Target className="h-5 w-5 mr-2 text-theme-600" />
                          赋能方案
                        </h4>
                        <div className="bg-theme-50 rounded-lg p-4">
                          <p className="text-gray-700 leading-relaxed text-sm">
                            {caseItem.solution}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Cases;
